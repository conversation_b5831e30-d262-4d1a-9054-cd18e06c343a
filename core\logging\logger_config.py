"""
Logger configuration for the Voice Agents Platform.

This module provides configuration utilities for setting up the custom logger
with appropriate settings for different environments (development, testing, production).
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger import get_logger, close_global_logger, create_module_logger, Logger


class LoggerConfig:
    """Configuration class for the Voice Agents Platform logger."""
    
    # Default configurations for different environments
    ENVIRONMENTS = {
        "development": {
            "log_dir": "logs",
            "verbosity": "debug",
            "max_bytes": 10**6,  # 1MB
            "backup_count": 5,
            "console_log_level": "warn",  # Only show warnings and errors in console
            "console_output": True
        },
        "testing": {
            "log_dir": "voice_agent_test_logs",
            "verbosity": "debug", 
            "max_bytes": 10000,  # 10KB for frequent rotation in tests
            "backup_count": 2,
            "console_log_level": "error",  # Only show errors in console during tests
            "console_output": True
        },
        "production": {
            "log_dir": "logs",
            "verbosity": "info",
            "max_bytes": 50 * 10**6,  # 50MB
            "backup_count": 10,
            "console_log_level": "error",  # Only show errors in console in production
            "console_output": False  # Disable console output in production
        }
    }
    
    @classmethod
    def setup_logger(cls, environment: str = "development", custom_config: Optional[Dict[str, Any]] = None) -> Logger:
        """
        Set up the global logger with environment-specific configuration.
        
        Args:
            environment: Environment name ("development", "testing", "production")
            custom_config: Optional custom configuration to override defaults
            
        Returns:
            Logger: The configured global logger instance
            
        Raises:
            ValueError: If environment is not recognized
        """
        if environment not in cls.ENVIRONMENTS:
            raise ValueError(f"Unknown environment: {environment}. Must be one of {list(cls.ENVIRONMENTS.keys())}")
        
        # Get base configuration for environment
        config = cls.ENVIRONMENTS[environment].copy()
        
        # Apply custom overrides if provided
        if custom_config:
            config.update(custom_config)
        
        # Create logger with configuration
        logger = get_logger(**config)
        
        return logger
    
    @classmethod
    def setup_for_development(cls) -> Logger:
        """Set up logger for development environment."""
        return cls.setup_logger("development")
    
    @classmethod
    def setup_for_testing(cls) -> Logger:
        """Set up logger for testing environment."""
        return cls.setup_logger("testing")
    
    @classmethod
    def setup_for_production(cls) -> Logger:
        """Set up logger for production environment."""
        return cls.setup_logger("production")
    
    @classmethod
    def setup_from_env(cls) -> Logger:
        """
        Set up logger based on environment variables.
        
        Environment variables:
        - VOICE_AGENT_ENV: Environment name (default: "development")
        - VOICE_AGENT_LOG_DIR: Log directory (optional override)
        - VOICE_AGENT_LOG_LEVEL: Log verbosity (optional override)
        - VOICE_AGENT_LOG_MAX_BYTES: Max bytes per log file (optional override)
        - VOICE_AGENT_LOG_BACKUP_COUNT: Number of backup files (optional override)
        - VOICE_AGENT_CONSOLE_LOG_LEVEL: Console output log level (optional override)
        
        Returns:
            Logger: The configured global logger instance
        """
        environment = os.getenv("VOICE_AGENT_ENV", "development")
        
        # Build custom config from environment variables
        custom_config = {}
        
        if os.getenv("VOICE_AGENT_LOG_DIR"):
            custom_config["log_dir"] = os.getenv("VOICE_AGENT_LOG_DIR")
        
        if os.getenv("VOICE_AGENT_LOG_LEVEL"):
            custom_config["verbosity"] = os.getenv("VOICE_AGENT_LOG_LEVEL")
        
        if os.getenv("VOICE_AGENT_LOG_MAX_BYTES"):
            try:
                custom_config["max_bytes"] = int(os.getenv("VOICE_AGENT_LOG_MAX_BYTES"))
            except ValueError:
                pass  # Use default if invalid
        
        if os.getenv("VOICE_AGENT_LOG_BACKUP_COUNT"):
            try:
                custom_config["backup_count"] = int(os.getenv("VOICE_AGENT_LOG_BACKUP_COUNT"))
            except ValueError:
                pass  # Use default if invalid
        
        if os.getenv("VOICE_AGENT_CONSOLE_LOG_LEVEL"):
            custom_config["console_log_level"] = os.getenv("VOICE_AGENT_CONSOLE_LOG_LEVEL")
        
        return cls.setup_logger(environment, custom_config)


def get_module_logger(module_name: str, session_id: str = None, state_id: str = None, trace_id: str = None):
    """
    Convenience function to get a module logger with automatic setup.
    
    This function ensures the global logger is initialized before creating a module logger.
    If no global logger exists, it sets up one for development by default.
    
    Args:
        module_name: Name of the module
        session_id: Optional session ID for context
        state_id: Optional state ID for context
        trace_id: Optional trace ID for context
        
    Returns:
        ModuleLogger: A module-specific logger wrapper
    """
    # Ensure global logger is set up
    try:
        get_logger()
    except:
        # If no global logger exists, set up default development logger
        LoggerConfig.setup_for_development()
    
    return create_module_logger(module_name, session_id, state_id, trace_id)


def cleanup_logger():
    """Clean up the global logger and close all resources."""
    close_global_logger()


# Convenience functions for common use cases
def setup_development_logging() -> Logger:
    """Set up logging for development environment."""
    return LoggerConfig.setup_for_development()


def setup_testing_logging() -> Logger:
    """Set up logging for testing environment."""
    return LoggerConfig.setup_for_testing()


def setup_production_logging() -> Logger:
    """Set up logging for production environment."""
    return LoggerConfig.setup_for_production()


def setup_logging_from_env() -> Logger:
    """Set up logging based on environment variables."""
    return LoggerConfig.setup_from_env()
