#!/usr/bin/env python3
"""
Examples of how to use the enhanced logger for debugging.

This script demonstrates different ways to control console output
to reduce terminal noise while debugging.
"""

import sys
from pathlib import Path

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import (
    get_module_logger, 
    enable_debug_console_output,
    disable_console_output,
    set_console_log_level,
    LoggerConfig
)

def example_1_default_behavior():
    """Example 1: Default behavior - only warnings and errors in console"""
    print("\n=== Example 1: Default Behavior ===")
    print("Only warnings and errors will show in console")
    
    # Set up logger with default settings (console_log_level="warn")
    LoggerConfig.setup_for_development()
    logger = get_module_logger("example_module", session_id="test_session")
    
    # These won't show in console (below warning level)
    logger.debug("This debug message won't show in console")
    logger.info("This info message won't show in console")
    
    # These will show in console
    logger.warning("⚠️ This warning will show in console")
    logger.error("❌ This error will show in console")
    
    print("Check the log files for all messages!")

def example_2_enable_debug_output():
    """Example 2: Enable debug output for detailed debugging"""
    print("\n=== Example 2: Enable Debug Output ===")
    print("All log levels will show in console")
    
    # Enable debug console output
    enable_debug_console_output()
    logger = get_module_logger("debug_module", session_id="debug_session")
    
    # All these will show in console
    logger.debug("🔍 This debug message will show in console")
    logger.info("ℹ️ This info message will show in console")
    logger.warning("⚠️ This warning will show in console")
    logger.error("❌ This error will show in console")

def example_3_disable_console_output():
    """Example 3: Disable console output completely"""
    print("\n=== Example 3: Disable Console Output ===")
    print("No logs will show in console, only in files")
    
    # Disable console output
    disable_console_output()
    logger = get_module_logger("silent_module", session_id="silent_session")
    
    # None of these will show in console
    logger.debug("This debug message won't show in console")
    logger.info("This info message won't show in console")
    logger.warning("This warning won't show in console")
    logger.error("This error won't show in console")
    
    print("All logs are written to files only!")

def example_4_custom_console_level():
    """Example 4: Set custom console log level"""
    print("\n=== Example 4: Custom Console Level ===")
    print("Only errors will show in console")
    
    # Set console to only show errors
    set_console_log_level("error")
    logger = get_module_logger("custom_module", session_id="custom_session")
    
    # These won't show in console
    logger.debug("This debug message won't show in console")
    logger.info("This info message won't show in console")
    logger.warning("This warning won't show in console")
    
    # Only this will show in console
    logger.error("❌ Only errors show in console")

def example_5_environment_variables():
    """Example 5: Using environment variables"""
    print("\n=== Example 5: Environment Variables ===")
    print("Set these environment variables to control logging:")
    print("export VOICE_AGENT_CONSOLE_LOG_LEVEL=error")
    print("export VOICE_AGENT_CONSOLE_OUTPUT=false")
    print("export VOICE_AGENT_LOG_LEVEL=debug")
    
    # This will use environment variables if set
    LoggerConfig.setup_from_env()
    logger = get_module_logger("env_module", session_id="env_session")
    
    logger.info("This message respects environment variable settings")

def example_6_production_vs_development():
    """Example 6: Production vs Development settings"""
    print("\n=== Example 6: Production vs Development ===")
    
    # Development settings (verbose files, minimal console)
    print("Development settings:")
    dev_logger = LoggerConfig.setup_for_development()
    logger = get_module_logger("dev_module", session_id="dev_session")
    logger.warning("Development: verbose files, minimal console")
    
    # Production settings (minimal files, no console)
    print("Production settings:")
    prod_logger = LoggerConfig.setup_for_production()
    logger = get_module_logger("prod_module", session_id="prod_session")
    logger.error("Production: minimal files, no console")

def example_7_file_only_logging():
    """Example 7: File-only logging with info_in_file()"""
    print("\n=== Example 7: File-Only Logging ===")
    print("Using info_in_file() to log to files only")
    
    # Set up logger with default settings
    LoggerConfig.setup_for_development()
    logger = get_module_logger("file_only_module", session_id="file_session")
    
    # These will show in console (if console_log_level allows)
    logger.info("This info message might show in console")
    logger.warning("This warning will show in console")
    
    # These will ONLY go to files, never to console
    logger.info_in_file("This info message goes to files only")
    logger.debug_in_file("This debug message goes to files only")
    logger.warning_in_file("This warning goes to files only")
    logger.error_in_file("This error goes to files only")
    
    print("Check the log files - you'll see all messages there!")
    print("But only warnings/errors appeared in console.")

def example_8_mixed_logging_strategy():
    """Example 8: Mixed logging strategy for different types of information"""
    print("\n=== Example 8: Mixed Logging Strategy ===")
    print("Using both console and file-only logging strategically")
    
    LoggerConfig.setup_for_development()
    logger = get_module_logger("mixed_module", session_id="mixed_session")
    
    # Important events that should be visible in console
    logger.info("🚀 Application started successfully")
    logger.warning("⚠️ High memory usage detected")
    logger.error("❌ Database connection failed")
    
    # Detailed debugging info that clutters console - use file-only
    logger.info_in_file(
        "Detailed configuration loaded",
        action="load_config",
        input_data={"config_file": "app.yaml", "sections": ["database", "redis", "api"]},
        layer="configuration"
    )
    
    logger.debug_in_file(
        "Processing user request details",
        action="process_request",
        input_data={"user_id": "12345", "request_type": "balance_check", "timestamp": "2024-01-01T12:00:00Z"},
        layer="request_processing"
    )
    
    logger.info_in_file(
        "Database query executed",
        action="db_query",
        input_data={"query": "SELECT * FROM users WHERE id = ?", "params": ["12345"]},
        output_data={"rows_returned": 1, "execution_time_ms": 15.2},
        metrics={"duration_ms": 15.2},
        layer="database"
    )
    
    print("Console shows important events, files contain detailed debugging info!")

def example_9_agent_logging_pattern():
    """Example 9: Agent logging pattern - what agents typically log"""
    print("\n=== Example 9: Agent Logging Pattern ===")
    print("Simulating how agents would use mixed logging")
    
    LoggerConfig.setup_for_development()
    logger = get_module_logger("stt_agent", session_id="agent_session")
    
    # Agent lifecycle events - visible in console
    logger.info("🎤 STT Agent initialized")
    logger.info("🎵 Processing audio file: user_input.wav")
    logger.info("✅ Audio transcription completed")
    
    # Detailed processing info - file only
    logger.info_in_file(
        "Audio processing details",
        action="process_audio",
        input_data={
            "file_path": "user_input.wav",
            "file_size_bytes": 1024000,
            "duration_seconds": 5.2,
            "sample_rate": 16000
        },
        layer="audio_processing"
    )
    
    logger.debug_in_file(
        "STT model inference details",
        action="model_inference",
        input_data={"model_name": "whisper-large", "language": "en"},
        output_data={"confidence": 0.95, "words": 15},
        metrics={"inference_time_ms": 1200, "memory_usage_mb": 512},
        layer="model_inference"
    )
    
    logger.info_in_file(
        "Transcription result",
        action="transcription_complete",
        output_data={
            "transcript": "Hello, I would like to check my account balance",
            "confidence": 0.95,
            "language": "en",
            "word_count": 10
        },
        layer="transcription"
    )
    
    print("Agent console output is clean, detailed info is in files!")

if __name__ == "__main__":
    print("🔧 Logger Debug Examples")
    print("=" * 50)
    
    # Run examples
    example_1_default_behavior()
    example_2_enable_debug_output()
    example_3_disable_console_output()
    example_4_custom_console_level()
    example_5_environment_variables()
    example_6_production_vs_development()
    example_7_file_only_logging()
    example_8_mixed_logging_strategy()
    example_9_agent_logging_pattern()
    
    print("\n✅ All examples completed!")
    print("Check the log files in the 'logs' directory for all messages.") 