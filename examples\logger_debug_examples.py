#!/usr/bin/env python3
"""
Examples of how to use the enhanced logger for debugging.

This script demonstrates different ways to control console output
to reduce terminal noise while debugging.
"""

import sys
from pathlib import Path

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import (
    get_module_logger, 
    enable_debug_console_output,
    disable_console_output,
    set_console_log_level,
    LoggerConfig
)

def example_1_default_behavior():
    """Example 1: Default behavior - only warnings and errors in console"""
    print("\n=== Example 1: Default Behavior ===")
    print("Only warnings and errors will show in console")
    
    # Set up logger with default settings (console_log_level="warn")
    LoggerConfig.setup_for_development()
    logger = get_module_logger("example_module", session_id="test_session")
    
    # These won't show in console (below warning level)
    logger.debug("This debug message won't show in console")
    logger.info("This info message won't show in console")
    
    # These will show in console
    logger.warning("⚠️ This warning will show in console")
    logger.error("❌ This error will show in console")
    
    print("Check the log files for all messages!")

def example_2_enable_debug_output():
    """Example 2: Enable debug output for detailed debugging"""
    print("\n=== Example 2: Enable Debug Output ===")
    print("All log levels will show in console")
    
    # Enable debug console output
    enable_debug_console_output()
    logger = get_module_logger("debug_module", session_id="debug_session")
    
    # All these will show in console
    logger.debug("🔍 This debug message will show in console")
    logger.info("ℹ️ This info message will show in console")
    logger.warning("⚠️ This warning will show in console")
    logger.error("❌ This error will show in console")

def example_3_disable_console_output():
    """Example 3: Disable console output completely"""
    print("\n=== Example 3: Disable Console Output ===")
    print("No logs will show in console, only in files")
    
    # Disable console output
    disable_console_output()
    logger = get_module_logger("silent_module", session_id="silent_session")
    
    # None of these will show in console
    logger.debug("This debug message won't show in console")
    logger.info("This info message won't show in console")
    logger.warning("This warning won't show in console")
    logger.error("This error won't show in console")
    
    print("All logs are written to files only!")

def example_4_custom_console_level():
    """Example 4: Set custom console log level"""
    print("\n=== Example 4: Custom Console Level ===")
    print("Only errors will show in console")
    
    # Set console to only show errors
    set_console_log_level("error")
    logger = get_module_logger("custom_module", session_id="custom_session")
    
    # These won't show in console
    logger.debug("This debug message won't show in console")
    logger.info("This info message won't show in console")
    logger.warning("This warning won't show in console")
    
    # Only this will show in console
    logger.error("❌ Only errors show in console")

def example_5_environment_variables():
    """Example 5: Using environment variables"""
    print("\n=== Example 5: Environment Variables ===")
    print("Set these environment variables to control logging:")
    print("export VOICE_AGENT_CONSOLE_LOG_LEVEL=error")
    print("export VOICE_AGENT_CONSOLE_OUTPUT=false")
    print("export VOICE_AGENT_LOG_LEVEL=debug")
    
    # This will use environment variables if set
    LoggerConfig.setup_from_env()
    logger = get_module_logger("env_module", session_id="env_session")
    
    logger.info("This message respects environment variable settings")

def example_6_production_vs_development():
    """Example 6: Production vs Development settings"""
    print("\n=== Example 6: Production vs Development ===")
    
    # Development settings (verbose files, minimal console)
    print("Development settings:")
    dev_logger = LoggerConfig.setup_for_development()
    logger = get_module_logger("dev_module", session_id="dev_session")
    logger.warning("Development: verbose files, minimal console")
    
    # Production settings (minimal files, no console)
    print("Production settings:")
    prod_logger = LoggerConfig.setup_for_production()
    logger = get_module_logger("prod_module", session_id="prod_session")
    logger.error("Production: minimal files, no console")

if __name__ == "__main__":
    print("🔧 Logger Debug Examples")
    print("=" * 50)
    
    # Run examples
    example_1_default_behavior()
    example_2_enable_debug_output()
    example_3_disable_console_output()
    example_4_custom_console_level()
    example_5_environment_variables()
    example_6_production_vs_development()
    
    print("\n✅ All examples completed!")
    print("Check the log files in the 'logs' directory for all messages.") 