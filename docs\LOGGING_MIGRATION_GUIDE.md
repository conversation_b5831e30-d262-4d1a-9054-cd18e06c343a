# Logging Migration Guide: Using `info_in_file()` for Better Debugging

## Overview

The Voice Agents Platform now supports **file-only logging** with the new `info_in_file()` method and related functions. This allows developers to have fine-grained control over what appears in the terminal vs. what gets logged to files for later analysis.

## The Problem

Previously, developers used `logger.info()` extensively, which could flood the terminal with logs, making debugging difficult. The new approach gives you control over:

- **Console Output**: What you see in the terminal during development
- **File Logging**: What gets saved to log files for detailed analysis

## New Methods Available

### File-Only Logging Methods
```python
logger.info_in_file("Message goes to files only")
logger.debug_in_file("Debug info goes to files only")
logger.warning_in_file("Warning goes to files only")
logger.error_in_file("Error goes to files only")
```

### Traditional Logging Methods (Console + Files)
```python
logger.info("Message goes to console AND files")
logger.debug("Debug info goes to console AND files")
logger.warning("Warning goes to console AND files")
logger.error("Error goes to console AND files")
```

## Migration Strategy

### Before (Old Pattern)
```python
# This would flood the console with all info messages
logger.info("Processing user request", action="process_request")
logger.info("Database query executed", action="db_query")
logger.info("Response generated", action="generate_response")
logger.info("User request completed", action="complete")
```

### After (New Pattern)
```python
# Console shows only important events
logger.info("🚀 Processing user request started")

# Detailed info goes to files only
logger.info_in_file("Database query executed", action="db_query")
logger.info_in_file("Response generated", action="generate_response")

# Important completion event shows in console
logger.info("✅ User request completed successfully")
```

## Best Practices

### 1. Console Logging (Use `logger.info()`)
- **Application lifecycle events**: Startup, shutdown, major state changes
- **User-facing events**: Request started, completed, errors
- **Important warnings and errors**: Things that need immediate attention
- **High-level progress**: Major steps in workflows

### 2. File-Only Logging (Use `logger.info_in_file()`)
- **Detailed processing info**: Step-by-step execution details
- **Configuration details**: Settings, parameters, environment info
- **Performance metrics**: Timing, memory usage, resource consumption
- **Debug information**: Internal state, intermediate results
- **Verbose data**: Large input/output data, detailed error context

### 3. Agent-Specific Patterns

#### STT Agent Example
```python
# Console: High-level progress
logger.info("🎤 STT Agent processing audio")
logger.info("✅ Transcription completed")

# Files: Detailed processing info
logger.info_in_file(
    "Audio processing details",
    action="process_audio",
    input_data={"file_size": 1024000, "duration": 5.2},
    layer="audio_processing"
)

logger.debug_in_file(
    "Model inference details",
    action="model_inference",
    metrics={"inference_time_ms": 1200, "memory_usage_mb": 512},
    layer="model_inference"
)
```

#### Processing Agent Example
```python
# Console: Important events
logger.info("🧠 Processing user intent")
logger.info("✅ Intent classification completed")

# Files: Detailed analysis
logger.info_in_file(
    "Intent analysis details",
    action="analyze_intent",
    input_data={"text": user_input, "confidence_threshold": 0.8},
    output_data={"intent": "check_balance", "confidence": 0.95},
    layer="intent_analysis"
)
```

## Migration Examples

### Example 1: State Manager
```python
# Before
logger.info("Starting state execution", action="execute_step")
logger.info("State execution completed", action="execute_step")

# After
logger.info("🔄 Starting state execution")
logger.info_in_file("State execution details", action="execute_step")
logger.info("✅ State execution completed")
```

### Example 2: Orchestrator
```python
# Before
logger.info("LLM evaluation started", action="evaluate_with_llm")
logger.info("LLM decision: proceed", action="evaluate_with_llm")

# After
logger.info("🤖 LLM evaluation started")
logger.info_in_file("LLM evaluation details", action="evaluate_with_llm")
logger.info("✅ LLM decision: proceed")
```

### Example 3: Memory Manager
```python
# Before
logger.info("Saving to memory", action="save_to_memory")
logger.info("Memory save completed", action="save_to_memory")

# After
logger.info("💾 Saving to memory")
logger.info_in_file("Memory operation details", action="save_to_memory")
logger.info("✅ Memory save completed")
```

## Environment-Specific Usage

### Development
```python
# Use mixed approach for debugging
logger.info("🚀 Feature X started")
logger.info_in_file("Detailed feature X processing")
logger.info("✅ Feature X completed")
```

### Production
```python
# Console shows only critical events
logger.info("🚀 Application started")
logger.info_in_file("Detailed startup sequence")
logger.error("❌ Critical error occurred")
```

### Testing
```python
# Console shows test progress
logger.info("🧪 Running test suite")
logger.info_in_file("Detailed test execution")
logger.info("✅ Test suite completed")
```

## Benefits

1. **Cleaner Console**: Only important events appear in terminal
2. **Comprehensive Logging**: All details still captured in files
3. **Better Debugging**: Easy to find important events in console
4. **Performance Analysis**: Detailed metrics in files for analysis
5. **Flexible Control**: Choose what to show based on context

## Quick Reference

| Use Case | Method | Console | Files |
|----------|--------|---------|-------|
| Important events | `logger.info()` | ✅ | ✅ |
| Detailed debugging | `logger.info_in_file()` | ❌ | ✅ |
| Errors/Warnings | `logger.error()` / `logger.warning()` | ✅ | ✅ |
| Verbose debugging | `logger.debug_in_file()` | ❌ | ✅ |

## Migration Checklist

- [ ] Identify high-level events that should appear in console
- [ ] Move detailed processing info to `info_in_file()`
- [ ] Keep errors and warnings using traditional methods
- [ ] Test console output is clean and informative
- [ ] Verify all details are still captured in log files
- [ ] Update team documentation with new patterns

This approach gives you the best of both worlds: a clean, informative console for development and comprehensive logging files for detailed analysis and debugging. 